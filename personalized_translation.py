#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
个性化翻译脚本 - 根据每个老师的具体情况生成不同的中文介绍
"""

import pandas as pd
import re
import time
import html
import random

def comprehensive_text_cleaning(text):
    """全面清理文本内容"""
    if pd.isna(text) or text == '':
        return ''
    
    text = str(text)
    
    # HTML解码和清理
    try:
        text = html.unescape(text)
    except:
        pass
    
    # 替换HTML实体
    html_entities = {
        '&nbsp;': ' ', '&amp;': '&', '&lt;': '<', '&gt;': '>',
        '&quot;': '"', '&#39;': "'", '&apos;': "'",
    }
    
    for entity, replacement in html_entities.items():
        text = text.replace(entity, replacement)
    
    # 移除HTML标签
    text = re.sub(r'<[^>]+>', ' ', text)
    
    # 清理特殊字符
    text = re.sub(r'[^\w\s.,!?;:\'\"-]', ' ', text)
    
    # 清理空白字符
    text = re.sub(r'\s+', ' ', text).strip()
    
    return text

def extract_detailed_info(text):
    """提取详细的个人信息"""
    info = {
        'name': '老师',
        'nationality': None,
        'age': None,
        'experience_years': None,
        'education': None,
        'specialties': [],
        'hobbies': [],
        'personality': [],
        'teaching_style': [],
        'certifications': []
    }
    
    if not text:
        return info
    
    text_lower = text.lower()
    
    # 提取姓名
    name_patterns = [
        r"(?:I'm|I am)\s+(?:Teacher\s+)?([A-Za-z]+)",
        r"This is\s+(?:Teacher\s+)?([A-Za-z]+)",
        r"My name is\s+(?:Teacher\s+)?([A-Za-z]+)",
        r"call me\s+([A-Za-z]+)",
        r"Teacher\s+([A-Za-z]+)"
    ]
    
    for pattern in name_patterns:
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            name = match.group(1).strip()
            excluded = {'teacher', 'from', 'the', 'and', 'with', 'here', 'your', 'years', 'old'}
            if len(name) > 1 and name.lower() not in excluded:
                info['name'] = name.capitalize()
                break
    
    # 提取国籍
    countries = {
        'philippines': '菲律宾', 'filipino': '菲律宾', 'pinoy': '菲律宾',
        'american': '美国', 'usa': '美国', 'united states': '美国',
        'british': '英国', 'uk': '英国', 'england': '英国',
        'canadian': '加拿大', 'canada': '加拿大',
        'australian': '澳大利亚', 'australia': '澳大利亚',
        'south africa': '南非', 'indian': '印度', 'india': '印度'
    }
    
    for eng, cn in countries.items():
        if eng in text_lower:
            info['nationality'] = cn
            break
    
    # 提取年龄
    age_match = re.search(r'(\d+)\s*years?\s*old', text_lower)
    if age_match:
        info['age'] = int(age_match.group(1))
    
    # 提取教学经验
    exp_patterns = [
        r'(\d+)\s*years?\s*(?:of\s*)?(?:teaching|experience)',
        r'teaching\s*(?:for\s*)?(\d+)\s*years?',
        r'more than\s*(\d+)\s*years?',
        r'over\s*(\d+)\s*years?'
    ]
    
    for pattern in exp_patterns:
        match = re.search(pattern, text_lower)
        if match:
            info['experience_years'] = int(match.group(1))
            break
    
    # 提取教育背景
    education_keywords = {
        'bachelor': '学士学位', 'master': '硕士学位', 'phd': '博士学位',
        'university': '大学', 'college': '学院', 'graduate': '毕业生',
        'degree': '学位', 'major': '专业', 'professor': '教授'
    }
    
    for keyword, cn in education_keywords.items():
        if keyword in text_lower:
            info['education'] = cn
            break
    
    # 提取专长
    specialties = {
        'conversation': '口语对话', 'speaking': '英语口语', 'pronunciation': '发音',
        'grammar': '语法', 'business english': '商务英语', 'kids': '少儿英语',
        'children': '儿童英语', 'adult': '成人英语', 'beginner': '初级英语',
        'advanced': '高级英语', 'ielts': '雅思', 'toefl': '托福',
        'writing': '写作', 'reading': '阅读', 'listening': '听力'
    }
    
    for eng, cn in specialties.items():
        if eng in text_lower:
            info['specialties'].append(cn)
    
    # 提取爱好
    hobbies = {
        'music': '音乐', 'singing': '唱歌', 'dancing': '跳舞', 'reading': '阅读',
        'movies': '电影', 'travel': '旅行', 'cooking': '烹饪', 'sports': '运动',
        'games': '游戏', 'art': '艺术', 'photography': '摄影', 'swimming': '游泳'
    }
    
    for eng, cn in hobbies.items():
        if eng in text_lower:
            info['hobbies'].append(cn)
    
    # 提取性格特点
    personality_traits = {
        'friendly': '友好', 'patient': '耐心', 'enthusiastic': '热情',
        'energetic': '充满活力', 'creative': '有创意', 'funny': '幽默',
        'kind': '善良', 'helpful': '乐于助人', 'passionate': '充满激情'
    }
    
    for eng, cn in personality_traits.items():
        if eng in text_lower:
            info['personality'].append(cn)
    
    return info

def create_personalized_intro(english_text):
    """创建个性化的中文介绍"""
    try:
        if not english_text or pd.isna(english_text):
            return ""
        
        # 清理文本
        clean_text = comprehensive_text_cleaning(english_text)
        
        if len(clean_text) < 15:
            return ""
        
        # 提取详细信息
        info = extract_detailed_info(clean_text)
        
        # 开头问候语变化
        greetings = [
            f"大家好！我是{info['name']}",
            f"你好！我是{info['name']}老师",
            f"Hello！我是{info['name']}",
            f"很高兴认识大家，我是{info['name']}"
        ]
        
        intro_parts = [random.choice(greetings)]
        
        # 添加国籍和年龄信息
        if info['nationality']:
            if info['age']:
                intro_parts.append(f"来自{info['nationality']}，{info['age']}岁")
            else:
                intro_parts.append(f"来自{info['nationality']}")
        elif info['age']:
            intro_parts.append(f"{info['age']}岁")
        
        # 添加教育背景
        if info['education']:
            intro_parts.append(f"拥有{info['education']}")
        
        # 添加教学经验
        if info['experience_years']:
            if info['experience_years'] > 10:
                exp_desc = f"有{info['experience_years']}年丰富的教学经验"
            elif info['experience_years'] >= 5:
                exp_desc = f"有{info['experience_years']}年的教学经验"
            else:
                exp_desc = f"有{info['experience_years']}年的教学经验"
        else:
            exp_desc = "拥有丰富的教学经验"
        
        intro_parts.append(exp_desc)
        
        # 添加专长
        if info['specialties']:
            specialties_text = "、".join(info['specialties'][:2])
            intro_parts.append(f"擅长{specialties_text}")
        else:
            intro_parts.append("专业教授英语")
        
        # 添加性格特点
        if info['personality']:
            personality_text = "、".join(info['personality'][:2])
            intro_parts.append(f"性格{personality_text}")
        
        # 添加爱好
        if info['hobbies']:
            hobbies_text = "、".join(info['hobbies'][:2])
            intro_parts.append(f"喜欢{hobbies_text}")
        
        # 教学风格描述（多样化）
        teaching_styles = [
            "我的课堂轻松愉快，注重实用性",
            "我喜欢互动式教学，让学习更有趣",
            "我会根据学生的需求定制课程",
            "我注重培养学生的学习兴趣",
            "我的教学方式生动活泼",
            "我善于营造轻松的学习氛围"
        ]
        
        # 结尾承诺（多样化）
        endings = [
            "期待在课堂上与你相见！",
            "让我们一起快乐学英语吧！",
            "相信我能帮助你提高英语水平！",
            "期待与你一起开启英语学习之旅！",
            "让我们一起在英语的世界里探索吧！",
            "我会帮助你实现英语学习目标！"
        ]
        
        # 组合完整介绍
        main_intro = "，".join(intro_parts) + "。"
        teaching_style = random.choice(teaching_styles)
        ending = random.choice(endings)
        
        full_intro = main_intro + teaching_style + "，" + ending
        
        return full_intro
        
    except Exception as e:
        return ""

def main():
    print("=== 个性化翻译工具 ===")
    
    # 读取已清理的Excel文件
    print("正在读取已清理的Excel文件...")
    try:
        df = pd.read_excel('20250728老师表_清理并翻译完成.xlsx')
        print(f"读取成功，共{len(df)}行数据")
    except Exception as e:
        print(f"读取Excel文件失败: {e}")
        return
    
    # 重新生成个性化的desc_cn
    print("开始生成个性化翻译...")
    start_time = time.time()
    
    processed_count = 0
    success_count = 0
    
    # 逐行处理
    for i in range(len(df)):
        try:
            # 检查是否有清理后的desc内容
            if pd.notna(df.at[i, 'desc_cleaned']) and str(df.at[i, 'desc_cleaned']).strip() != '':
                cleaned_desc = df.at[i, 'desc_cleaned']
                
                # 生成个性化中文介绍
                personalized_intro = create_personalized_intro(cleaned_desc)
                
                if personalized_intro:
                    df.at[i, 'desc_cn'] = personalized_intro
                    success_count += 1
                
                processed_count += 1
                
                # 显示进度
                if processed_count % 5000 == 0:
                    elapsed_time = time.time() - start_time
                    progress = processed_count / len(df) * 100
                    print(f"处理进度: {processed_count}/{len(df)} ({progress:.1f}%) - 成功翻译: {success_count} - 耗时: {elapsed_time/60:.1f}分钟")
                    
        except Exception as e:
            # 静默处理错误，继续下一行
            pass
    
    print(f"个性化翻译完成！共处理 {processed_count} 行，成功翻译 {success_count} 行")
    
    # 保存结果
    output_file = '20250728老师表_个性化翻译完成.xlsx'
    print(f"正在保存到: {output_file}")
    
    try:
        df.to_excel(output_file, index=False)
        print("保存成功！")
        
        # 最终统计
        total_translated = (df['desc_cn'].notna() & (df['desc_cn'] != '')).sum()
        
        print(f"\n最终统计:")
        print(f"- 总数据行数: {len(df)}")
        print(f"- 个性化翻译的行数: {total_translated}")
        
        # 显示个性化翻译示例
        print("\n个性化翻译示例:")
        sample_count = 0
        shown_intros = set()
        
        for i in range(len(df)):
            if (pd.notna(df.at[i, 'desc_cn']) and str(df.at[i, 'desc_cn']).strip() != '' and
                sample_count < 8):
                
                intro = str(df.at[i, 'desc_cn'])
                # 确保显示不同的翻译
                if intro not in shown_intros:
                    shown_intros.add(intro)
                    print(f"\n示例 {sample_count + 1}:")
                    print(f"原文: {str(df.at[i, 'desc'])[:80]}...")
                    print(f"译文: {intro}")
                    sample_count += 1
        
        end_time = time.time()
        print(f"\n总耗时: {(end_time - start_time)/60:.2f}分钟")
        
    except Exception as e:
        print(f"保存失败: {e}")

if __name__ == "__main__":
    main()
