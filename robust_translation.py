#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
稳定版外教自我介绍翻译脚本
"""

import pandas as pd
import re
import time

def safe_extract_name(text):
    """安全提取老师姓名"""
    try:
        if not text or len(str(text).strip()) < 5:
            return "老师"
        
        text = str(text)
        # 简单的姓名提取
        patterns = [
            r"I'm\s+(?:Teacher\s+)?(\w+)",
            r"I am\s+(?:Teacher\s+)?(\w+)",
            r"This is\s+(?:Teacher\s+)?(\w+)",
            r"My name is\s+(?:Teacher\s+)?(\w+)",
            r"Teacher\s+(\w+)"
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                name = match.group(1)
                if len(name) > 1 and name.lower() not in ['teacher', 'from', 'the', 'and', 'with', 'here', 'your']:
                    return name
        
        return "老师"
    except:
        return "老师"

def safe_extract_country(text):
    """安全提取国籍"""
    try:
        if not text:
            return None
        
        text = str(text).lower()
        countries = {
            'philippines': '菲律宾',
            'filipino': '菲律宾',
            'american': '美国',
            'usa': '美国',
            'british': '英国',
            'uk': '英国',
            'canadian': '加拿大',
            'australia': '澳大利亚',
            'india': '印度',
            'south africa': '南非'
        }
        
        for eng, cn in countries.items():
            if eng in text:
                return cn
        return None
    except:
        return None

def safe_extract_years(text):
    """安全提取教学年限"""
    try:
        if not text:
            return "丰富"
        
        text = str(text)
        # 查找年限
        match = re.search(r'(\d+)\s*years?', text, re.IGNORECASE)
        if match:
            years = int(match.group(1))
            if years > 15:
                return "15年以上"
            elif years > 10:
                return f"{years}年"
            elif years >= 3:
                return f"{years}年"
            else:
                return "丰富"
        return "丰富"
    except:
        return "丰富"

def create_simple_intro(english_text):
    """创建简单的中文介绍"""
    try:
        if not english_text or pd.isna(english_text):
            return ""
        
        text = str(english_text).strip()
        
        # 过滤无效内容
        if len(text) < 10 or text.lower() in ['none', 'asd', 'test', 'null', 'n/a', '']:
            return ""
        
        # 清理HTML标签
        text = re.sub(r'<[^>]+>', '', text)
        text = re.sub(r'&\w+;', ' ', text)
        text = re.sub(r'\s+', ' ', text).strip()
        
        if len(text) < 10:
            return ""
        
        # 提取信息
        name = safe_extract_name(text)
        country = safe_extract_country(text)
        years = safe_extract_years(text)
        
        # 构建介绍
        if country:
            intro = f"大家好！我是{name}，来自{country}，拥有{years}的教学经验，专业教授英语。我的课堂生动有趣，注重互动交流，致力于帮助每位学生提高英语水平，期待与您一起开启精彩的英语学习之旅！"
        else:
            intro = f"大家好！我是{name}，拥有{years}的教学经验，专业教授英语。我的课堂生动有趣，注重互动交流，致力于帮助每位学生提高英语水平，期待与您一起开启精彩的英语学习之旅！"
        
        return intro
    except Exception as e:
        return ""

def main():
    print("=== 外教自我介绍翻译工具（稳定版）===")
    
    # 读取Excel文件
    print("正在读取Excel文件...")
    try:
        df = pd.read_excel('20250728老师表有英文介绍没有中文介绍全量数据.xlsx')
        print(f"读取成功，共{len(df)}行数据")
    except Exception as e:
        print(f"读取Excel文件失败: {e}")
        return
    
    # 确保有desc_cn列
    if 'desc_cn' not in df.columns:
        df['desc_cn'] = ''
        print("已创建desc_cn列")
    
    print("开始翻译处理...")
    start_time = time.time()
    
    processed_count = 0
    success_count = 0
    
    # 逐行处理
    for i in range(len(df)):
        try:
            # 检查是否需要翻译
            if (pd.notna(df.at[i, 'desc']) and 
                str(df.at[i, 'desc']).strip() != '' and
                (pd.isna(df.at[i, 'desc_cn']) or str(df.at[i, 'desc_cn']).strip() == '')):
                
                english_desc = df.at[i, 'desc']
                chinese_intro = create_simple_intro(english_desc)
                
                if chinese_intro:
                    df.at[i, 'desc_cn'] = chinese_intro
                    success_count += 1
                
                processed_count += 1
                
                # 显示进度
                if processed_count % 5000 == 0:
                    elapsed_time = time.time() - start_time
                    progress = processed_count / len(df) * 100
                    print(f"处理进度: {processed_count}/{len(df)} ({progress:.1f}%) - 成功翻译: {success_count} - 耗时: {elapsed_time/60:.1f}分钟")
                    
        except Exception as e:
            # 静默处理错误，继续下一行
            pass
    
    print(f"翻译处理完成！共处理 {processed_count} 行，成功翻译 {success_count} 行")
    
    # 保存结果
    output_file = '20250728老师表有英文介绍没有中文介绍全量数据_final_translated.xlsx'
    print(f"正在保存到: {output_file}")
    
    try:
        df.to_excel(output_file, index=False)
        print("保存成功！")
        
        # 最终统计
        total_desc = (df['desc'].notna() & (df['desc'] != '')).sum()
        total_translated = (df['desc_cn'].notna() & (df['desc_cn'] != '')).sum()
        
        print(f"\n最终统计:")
        print(f"- 总数据行数: {len(df)}")
        print(f"- 有英文描述的行数: {total_desc}")
        print(f"- 已翻译的行数: {total_translated}")
        print(f"- 翻译完成率: {total_translated/total_desc*100:.1f}%")
        
        # 显示翻译示例
        print("\n翻译示例:")
        sample_count = 0
        for i in range(len(df)):
            if (pd.notna(df.at[i, 'desc']) and str(df.at[i, 'desc']).strip() != '' and 
                pd.notna(df.at[i, 'desc_cn']) and str(df.at[i, 'desc_cn']).strip() != ''):
                print(f"\n示例 {sample_count + 1}:")
                print(f"原文: {str(df.at[i, 'desc'])[:80]}...")
                print(f"译文: {df.at[i, 'desc_cn']}")
                sample_count += 1
                if sample_count >= 3:
                    break
        
        end_time = time.time()
        print(f"\n总耗时: {(end_time - start_time)/60:.2f}分钟")
        
    except Exception as e:
        print(f"保存失败: {e}")

if __name__ == "__main__":
    main()
