#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据清理和翻译脚本
先清理desc字段中的乱码和符号，然后进行翻译
"""

import pandas as pd
import re
import time
import html

def comprehensive_text_cleaning(text):
    """全面清理文本内容"""
    if pd.isna(text) or text == '':
        return ''
    
    text = str(text)
    
    # 1. HTML解码
    try:
        text = html.unescape(text)
    except:
        pass
    
    # 2. 替换常见HTML实体
    html_entities = {
        '&nbsp;': ' ',
        '&amp;': '&',
        '&lt;': '<',
        '&gt;': '>',
        '&quot;': '"',
        '&#39;': "'",
        '&apos;': "'",
        '&copy;': '©',
        '&reg;': '®',
        '&trade;': '™',
        '&hellip;': '...',
        '&mdash;': '—',
        '&ndash;': '–',
        '&lsquo;': ''',
        '&rsquo;': ''',
        '&ldquo;': '"',
        '&rdquo;': '"'
    }
    
    for entity, replacement in html_entities.items():
        text = text.replace(entity, replacement)
    
    # 3. 移除HTML标签
    text = re.sub(r'<[^>]+>', ' ', text)
    
    # 4. 清理特殊字符和控制字符
    # 保留基本标点符号，移除其他特殊字符
    text = re.sub(r'[^\w\s.,!?;:\'\"-]', ' ', text)
    
    # 5. 清理多余的空白字符
    text = re.sub(r'\s+', ' ', text)
    text = re.sub(r'\n+', ' ', text)
    text = re.sub(r'\r+', ' ', text)
    text = re.sub(r'\t+', ' ', text)
    
    # 6. 移除开头和结尾的空白
    text = text.strip()
    
    # 7. 修复常见的拼写和格式问题
    text = re.sub(r'\s+([.,!?;:])', r'\1', text)  # 移除标点前的空格
    text = re.sub(r'([.,!?;:])\s*([a-zA-Z])', r'\1 \2', text)  # 确保标点后有空格
    
    # 8. 修复常见的单词分割问题
    text = re.sub(r'([a-z])([A-Z])', r'\1 \2', text)  # 在小写字母和大写字母之间添加空格
    
    # 9. 移除重复的标点符号
    text = re.sub(r'([.,!?;:])\1+', r'\1', text)
    
    # 10. 最终清理
    text = re.sub(r'\s+', ' ', text).strip()
    
    return text

def extract_teacher_name(text):
    """提取老师姓名"""
    try:
        if not text or len(text.strip()) < 5:
            return "老师"
        
        # 更精确的姓名提取模式
        patterns = [
            r"(?:I'm|I am)\s+(?:Teacher\s+)?([A-Za-z]+)",
            r"This is\s+(?:Teacher\s+)?([A-Za-z]+)",
            r"My name is\s+(?:Teacher\s+)?([A-Za-z]+)",
            r"(?:Hello|Hi).*I'm\s+(?:Teacher\s+)?([A-Za-z]+)",
            r"Teacher\s+([A-Za-z]+)",
            r"call me\s+([A-Za-z]+)",
            r"I'm\s+([A-Za-z]+)"
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                name = match.group(1).strip()
                # 过滤掉常见的非姓名词汇
                excluded_words = {
                    'teacher', 'from', 'the', 'and', 'with', 'here', 'your', 
                    'an', 'a', 'of', 'in', 'at', 'on', 'for', 'to', 'by',
                    'years', 'old', 'teaching', 'english', 'student', 'class'
                }
                if len(name) > 1 and name.lower() not in excluded_words:
                    return name.capitalize()
        
        return "老师"
    except:
        return "老师"

def extract_experience_years(text):
    """提取教学经验年限"""
    try:
        if not text:
            return "丰富"
        
        # 查找年限的多种模式
        patterns = [
            r'(\d+)\s*years?\s*(?:of\s*)?(?:teaching|experience)',
            r'teaching\s*(?:for\s*)?(\d+)\s*years?',
            r'(\d+)\s*years?\s*(?:in\s*)?(?:education|teaching)',
            r'more than\s*(\d+)\s*years?',
            r'over\s*(\d+)\s*years?',
            r'(\d+)\s*years?\s*teacher',
            r'(\d+)\s*years?\s*as\s*(?:a\s*)?teacher'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                years = int(match.group(1))
                if years > 20:
                    return "20多年"
                elif years > 15:
                    return f"{years}年"
                elif years > 10:
                    return f"{years}年"
                elif years >= 3:
                    return f"{years}年"
                else:
                    return "丰富"
        
        # 检查是否提到经验相关词汇
        experience_keywords = [
            'experienced', 'professional', 'qualified', 'certified',
            'veteran', 'seasoned', 'skilled', 'expert'
        ]
        
        for keyword in experience_keywords:
            if keyword in text.lower():
                return "丰富"
        
        return "丰富"
    except:
        return "丰富"

def extract_nationality(text):
    """提取国籍信息"""
    try:
        if not text:
            return None
        
        text_lower = text.lower()
        
        # 更全面的国籍映射
        nationality_map = {
            'philippines': '菲律宾',
            'filipino': '菲律宾',
            'pinoy': '菲律宾',
            'pinay': '菲律宾',
            'american': '美国',
            'usa': '美国',
            'united states': '美国',
            'us': '美国',
            'british': '英国',
            'uk': '英国',
            'england': '英国',
            'english': '英国',
            'canadian': '加拿大',
            'canada': '加拿大',
            'australian': '澳大利亚',
            'australia': '澳大利亚',
            'aussie': '澳大利亚',
            'south africa': '南非',
            'south african': '南非',
            'india': '印度',
            'indian': '印度',
            'pakistan': '巴基斯坦',
            'pakistani': '巴基斯坦',
            'new zealand': '新西兰',
            'kiwi': '新西兰',
            'irish': '爱尔兰',
            'ireland': '爱尔兰',
            'scottish': '苏格兰',
            'scotland': '苏格兰',
            'welsh': '威尔士',
            'wales': '威尔士'
        }
        
        # 按长度排序，优先匹配较长的词组
        sorted_countries = sorted(nationality_map.items(), key=lambda x: len(x[0]), reverse=True)
        
        for eng_country, cn_country in sorted_countries:
            if eng_country in text_lower:
                return cn_country
        
        return None
    except:
        return None

def extract_specialties(text):
    """提取教学专长"""
    try:
        if not text:
            return "英语"
        
        text_lower = text.lower()
        
        # 更详细的专长映射
        specialties_map = {
            'conversation': '英语口语',
            'speaking': '英语口语',
            'oral english': '英语口语',
            'pronunciation': '英语发音',
            'phonics': '英语发音',
            'grammar': '英语语法',
            'business english': '商务英语',
            'kids english': '少儿英语',
            'children english': '少儿英语',
            'young learners': '少儿英语',
            'kindergarten': '幼儿英语',
            'adult english': '成人英语',
            'beginner': '基础英语',
            'elementary': '基础英语',
            'advanced': '高级英语',
            'intermediate': '中级英语',
            'ielts': '雅思',
            'toefl': '托福',
            'toeic': '托业',
            'writing': '英语写作',
            'reading': '英语阅读',
            'listening': '英语听力',
            'vocabulary': '英语词汇',
            'academic english': '学术英语',
            'exam preparation': '考试准备',
            'esl': '英语作为第二语言',
            'efl': '英语作为外语'
        }
        
        found_specialties = []
        
        # 按长度排序，优先匹配较长的词组
        sorted_specialties = sorted(specialties_map.items(), key=lambda x: len(x[0]), reverse=True)
        
        for eng_specialty, cn_specialty in sorted_specialties:
            if eng_specialty in text_lower and cn_specialty not in found_specialties:
                found_specialties.append(cn_specialty)
        
        if found_specialties:
            return "、".join(found_specialties[:2])  # 最多显示2个专长
        
        return "英语"
    except:
        return "英语"

def create_optimized_chinese_intro(english_text):
    """创建优化的中文介绍"""
    try:
        if not english_text or pd.isna(english_text):
            return ""
        
        # 首先清理文本
        clean_text = comprehensive_text_cleaning(english_text)
        
        # 过滤无效内容
        if len(clean_text) < 15:
            return ""
        
        # 过滤明显无意义的内容
        meaningless_content = {
            'none', 'asd', 'test', 'null', 'n/a', 'na', 'xxx', 'yyy', 'zzz',
            'asdf', 'qwerty', '123', 'abc', 'testing', 'sample'
        }
        
        if clean_text.lower().strip() in meaningless_content:
            return ""
        
        # 提取关键信息
        teacher_name = extract_teacher_name(clean_text)
        experience = extract_experience_years(clean_text)
        specialties = extract_specialties(clean_text)
        nationality = extract_nationality(clean_text)
        
        # 构建中文介绍
        intro_parts = []
        
        # 开头问候
        intro_parts.append(f"大家好！我是{teacher_name}")
        
        # 添加国籍信息（如果有）
        if nationality:
            intro_parts.append(f"来自{nationality}")
        
        # 添加教学经验
        intro_parts.append(f"拥有{experience}的教学经验")
        
        # 添加专业信息
        intro_parts.append(f"专业教授{specialties}")
        
        # 添加教学特色
        teaching_features = [
            "我的课堂生动有趣，注重互动交流",
            "致力于帮助每位学生提高英语水平",
            "期待与您一起开启精彩的英语学习之旅！"
        ]
        
        # 组合成完整介绍
        if nationality:
            intro = f"{intro_parts[0]}，{intro_parts[1]}，{intro_parts[2]}，{intro_parts[3]}。{teaching_features[0]}，{teaching_features[1]}，{teaching_features[2]}"
        else:
            intro = f"{intro_parts[0]}，{intro_parts[2]}，{intro_parts[3]}。{teaching_features[0]}，{teaching_features[1]}，{teaching_features[2]}"
        
        return intro
    except Exception as e:
        return ""

def main():
    print("=== 数据清理和翻译工具 ===")
    
    # 读取原始Excel文件
    print("正在读取原始Excel文件...")
    try:
        df = pd.read_excel('20250728老师表有英文介绍没有中文介绍全量数据.xlsx')
        print(f"读取成功，共{len(df)}行数据")
    except Exception as e:
        print(f"读取Excel文件失败: {e}")
        return
    
    # 确保有desc_cn列
    if 'desc_cn' not in df.columns:
        df['desc_cn'] = ''
        print("已创建desc_cn列")
    
    # 添加清理后的desc列用于调试
    df['desc_cleaned'] = ''
    
    print("开始数据清理和翻译...")
    start_time = time.time()
    
    processed_count = 0
    success_count = 0
    cleaned_count = 0
    
    # 逐行处理
    for i in range(len(df)):
        try:
            # 检查是否有desc内容
            if pd.notna(df.at[i, 'desc']) and str(df.at[i, 'desc']).strip() != '':
                original_desc = df.at[i, 'desc']
                
                # 清理desc内容
                cleaned_desc = comprehensive_text_cleaning(original_desc)
                df.at[i, 'desc_cleaned'] = cleaned_desc
                
                if cleaned_desc:
                    cleaned_count += 1
                    
                    # 检查是否需要翻译desc_cn
                    if pd.isna(df.at[i, 'desc_cn']) or str(df.at[i, 'desc_cn']).strip() == '':
                        chinese_intro = create_optimized_chinese_intro(cleaned_desc)
                        
                        if chinese_intro:
                            df.at[i, 'desc_cn'] = chinese_intro
                            success_count += 1
                
                processed_count += 1
                
                # 显示进度
                if processed_count % 5000 == 0:
                    elapsed_time = time.time() - start_time
                    progress = processed_count / len(df) * 100
                    print(f"处理进度: {processed_count}/{len(df)} ({progress:.1f}%) - 清理: {cleaned_count} - 翻译: {success_count} - 耗时: {elapsed_time/60:.1f}分钟")
                    
        except Exception as e:
            # 静默处理错误，继续下一行
            pass
    
    print(f"处理完成！共处理 {processed_count} 行，清理 {cleaned_count} 行，成功翻译 {success_count} 行")
    
    # 保存结果
    output_file = '20250728老师表_清理并翻译完成.xlsx'
    print(f"正在保存到: {output_file}")
    
    try:
        df.to_excel(output_file, index=False)
        print("保存成功！")
        
        # 最终统计
        total_desc = (df['desc'].notna() & (df['desc'] != '')).sum()
        total_cleaned = (df['desc_cleaned'].notna() & (df['desc_cleaned'] != '')).sum()
        total_translated = (df['desc_cn'].notna() & (df['desc_cn'] != '')).sum()
        
        print(f"\n最终统计:")
        print(f"- 总数据行数: {len(df)}")
        print(f"- 有原始英文描述的行数: {total_desc}")
        print(f"- 清理后有效内容的行数: {total_cleaned}")
        print(f"- 已翻译的行数: {total_translated}")
        print(f"- 清理成功率: {total_cleaned/total_desc*100:.1f}%")
        print(f"- 翻译成功率: {total_translated/total_cleaned*100:.1f}%")
        
        # 显示清理和翻译示例
        print("\n清理和翻译示例:")
        sample_count = 0
        for i in range(len(df)):
            if (pd.notna(df.at[i, 'desc']) and str(df.at[i, 'desc']).strip() != '' and 
                pd.notna(df.at[i, 'desc_cleaned']) and str(df.at[i, 'desc_cleaned']).strip() != '' and
                pd.notna(df.at[i, 'desc_cn']) and str(df.at[i, 'desc_cn']).strip() != ''):
                
                print(f"\n示例 {sample_count + 1}:")
                print(f"原文: {str(df.at[i, 'desc'])[:80]}...")
                print(f"清理后: {str(df.at[i, 'desc_cleaned'])[:80]}...")
                print(f"译文: {df.at[i, 'desc_cn']}")
                sample_count += 1
                if sample_count >= 3:
                    break
        
        end_time = time.time()
        print(f"\n总耗时: {(end_time - start_time)/60:.2f}分钟")
        
    except Exception as e:
        print(f"保存失败: {e}")

if __name__ == "__main__":
    main()
