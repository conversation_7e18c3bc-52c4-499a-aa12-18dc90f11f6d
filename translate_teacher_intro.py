#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
外教自我介绍翻译脚本
将Excel文件中的英文desc字段翻译为中文并填入desc_cn字段
"""

import pandas as pd
import time
import re
from typing import Optional
import requests
import json
import os
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

class TeacherIntroTranslator:
    def __init__(self, excel_file_path: str):
        """
        初始化翻译器
        
        Args:
            excel_file_path: Excel文件路径
        """
        self.excel_file_path = excel_file_path
        self.df = None
        self.translated_count = 0
        self.total_count = 0
        self.lock = threading.Lock()
        
    def load_excel(self):
        """加载Excel文件"""
        try:
            print("正在加载Excel文件...")
            self.df = pd.read_excel(self.excel_file_path)
            print(f"Excel文件加载成功！共{len(self.df)}行数据")
            
            # 检查必要的列
            if 'desc' not in self.df.columns:
                raise ValueError("Excel文件中未找到'desc'列")
            if 'desc_cn' not in self.df.columns:
                # 如果没有desc_cn列，创建一个
                self.df['desc_cn'] = ''
                print("已创建desc_cn列")
            
            # 统计需要翻译的数据
            need_translation = self.df['desc'].notna() & (self.df['desc'] != '') & (
                self.df['desc_cn'].isna() | (self.df['desc_cn'] == '')
            )
            self.total_count = need_translation.sum()
            print(f"需要翻译的条目数: {self.total_count}")
            
            return True
        except Exception as e:
            print(f"加载Excel文件失败: {e}")
            return False
    
    def clean_html_tags(self, text: str) -> str:
        """清理HTML标签和特殊字符"""
        if pd.isna(text) or text == '':
            return ''
        
        # 转换为字符串
        text = str(text)
        
        # 替换HTML实体
        text = text.replace('&nbsp;', ' ')
        text = text.replace('&amp;', '&')
        text = text.replace('&lt;', '<')
        text = text.replace('&gt;', '>')
        text = text.replace('&quot;', '"')
        
        # 移除HTML标签
        text = re.sub(r'<[^>]+>', '', text)
        
        # 清理多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        text = text.strip()
        
        return text
    
    def translate_text_simple(self, text: str) -> str:
        """
        简单的翻译函数，适合app展示的风格
        这里使用规则和模板来生成适合的中文介绍
        """
        if not text or text.strip() == '' or text.lower() in ['none', 'asd', 'test']:
            return ''
        
        # 清理HTML标签
        clean_text = self.clean_html_tags(text)
        
        if len(clean_text) < 10:  # 太短的文本跳过
            return ''
        
        # 这里可以集成真正的翻译API，现在先用模拟翻译
        # 为了演示，我们创建一个基本的翻译模板
        translated = self.create_chinese_intro_template(clean_text)
        
        return translated
    
    def create_chinese_intro_template(self, english_text: str) -> str:
        """
        根据英文内容创建适合app展示的中文介绍模板
        """
        # 提取关键信息
        name_match = re.search(r'(?:I\'m|I am|This is|My name is)\s+(?:Teacher\s+)?(\w+)', english_text, re.IGNORECASE)
        teacher_name = name_match.group(1) if name_match else "老师"
        
        # 检查教学经验
        experience_years = self.extract_experience_years(english_text)
        
        # 检查专业背景
        subjects = self.extract_subjects(english_text)
        
        # 检查国籍/地区
        nationality = self.extract_nationality(english_text)
        
        # 构建中文介绍
        intro_parts = []
        
        # 开头问候
        intro_parts.append(f"大家好！我是{teacher_name}老师")
        
        # 添加国籍信息
        if nationality:
            intro_parts.append(f"来自{nationality}")
        
        # 添加教学经验
        if experience_years:
            intro_parts.append(f"拥有{experience_years}年的教学经验")
        else:
            intro_parts.append("拥有丰富的教学经验")
        
        # 添加专业信息
        if subjects:
            intro_parts.append(f"专业教授{subjects}")
        else:
            intro_parts.append("专业教授英语")
        
        # 添加教学特色
        intro_parts.append("我的课堂生动有趣，注重互动交流")
        intro_parts.append("致力于帮助每位学生提高英语水平")
        intro_parts.append("期待与您一起开启精彩的英语学习之旅！")
        
        return "，".join(intro_parts[:3]) + "。" + "，".join(intro_parts[3:]) + "。"
    
    def extract_experience_years(self, text: str) -> Optional[str]:
        """提取教学年限"""
        patterns = [
            r'(\d+)\s*years?\s*(?:of\s*)?(?:teaching|experience)',
            r'teaching\s*(?:for\s*)?(\d+)\s*years?',
            r'(\d+)\s*years?\s*teacher'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                years = int(match.group(1))
                if years > 20:
                    return "20多"
                elif years > 10:
                    return "10多"
                elif years > 5:
                    return str(years)
                else:
                    return str(years)
        return None
    
    def extract_subjects(self, text: str) -> Optional[str]:
        """提取教学科目"""
        subjects_map = {
            'english': '英语',
            'math': '数学',
            'science': '科学',
            'chinese': '中文',
            'conversation': '英语口语',
            'grammar': '英语语法',
            'pronunciation': '英语发音',
            'business english': '商务英语',
            'kids english': '少儿英语'
        }
        
        found_subjects = []
        text_lower = text.lower()
        
        for eng_subject, cn_subject in subjects_map.items():
            if eng_subject in text_lower:
                found_subjects.append(cn_subject)
        
        if found_subjects:
            return "、".join(found_subjects[:2])  # 最多显示2个科目
        return None
    
    def extract_nationality(self, text: str) -> Optional[str]:
        """提取国籍信息"""
        nationality_map = {
            'philippines': '菲律宾',
            'filipino': '菲律宾',
            'american': '美国',
            'british': '英国',
            'canadian': '加拿大',
            'australian': '澳大利亚',
            'south africa': '南非',
            'india': '印度',
            'pakistan': '巴基斯坦'
        }
        
        text_lower = text.lower()
        for eng_country, cn_country in nationality_map.items():
            if eng_country in text_lower:
                return cn_country
        return None
    
    def translate_batch(self, batch_data):
        """批量翻译一组数据"""
        results = []
        for idx, row in batch_data.iterrows():
            try:
                english_desc = row['desc']
                translated = self.translate_text_simple(english_desc)
                results.append((idx, translated))
                
                with self.lock:
                    self.translated_count += 1
                    if self.translated_count % 100 == 0:
                        progress = (self.translated_count / self.total_count) * 100
                        print(f"翻译进度: {self.translated_count}/{self.total_count} ({progress:.1f}%)")
                        
            except Exception as e:
                print(f"翻译第{idx}行时出错: {e}")
                results.append((idx, ''))
                
        return results
    
    def process_translation(self, batch_size: int = 1000, max_workers: int = 4):
        """处理翻译任务"""
        if self.df is None:
            print("请先加载Excel文件")
            return False
        
        print("开始翻译处理...")
        
        # 筛选需要翻译的行
        need_translation_mask = (
            self.df['desc'].notna() & 
            (self.df['desc'] != '') & 
            (self.df['desc_cn'].isna() | (self.df['desc_cn'] == ''))
        )
        
        need_translation_df = self.df[need_translation_mask].copy()
        
        if len(need_translation_df) == 0:
            print("没有需要翻译的数据")
            return True
        
        print(f"开始翻译 {len(need_translation_df)} 条记录...")
        
        # 分批处理
        batches = []
        for i in range(0, len(need_translation_df), batch_size):
            batch = need_translation_df.iloc[i:i+batch_size]
            batches.append(batch)
        
        # 使用线程池并行处理
        all_results = []
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_batch = {executor.submit(self.translate_batch, batch): batch for batch in batches}
            
            for future in as_completed(future_to_batch):
                try:
                    results = future.result()
                    all_results.extend(results)
                except Exception as e:
                    print(f"批处理出错: {e}")
        
        # 更新DataFrame
        for idx, translated_text in all_results:
            self.df.at[idx, 'desc_cn'] = translated_text
        
        print(f"翻译完成！共处理 {len(all_results)} 条记录")
        return True
    
    def save_excel(self, output_path: Optional[str] = None):
        """保存翻译后的Excel文件"""
        if self.df is None:
            print("没有数据可保存")
            return False
        
        if output_path is None:
            # 生成输出文件名
            base_name = os.path.splitext(self.excel_file_path)[0]
            output_path = f"{base_name}_translated.xlsx"
        
        try:
            print(f"正在保存到: {output_path}")
            self.df.to_excel(output_path, index=False)
            print("保存成功！")
            return True
        except Exception as e:
            print(f"保存失败: {e}")
            return False
    
    def run(self):
        """运行完整的翻译流程"""
        print("=== 外教自我介绍翻译工具 ===")
        
        # 加载数据
        if not self.load_excel():
            return False
        
        # 处理翻译
        if not self.process_translation():
            return False
        
        # 保存结果
        if not self.save_excel():
            return False
        
        print("=== 翻译任务完成 ===")
        return True

def main():
    """主函数"""
    excel_file = "20250728老师表有英文介绍没有中文介绍全量数据.xlsx"
    
    if not os.path.exists(excel_file):
        print(f"错误：找不到文件 {excel_file}")
        return
    
    translator = TeacherIntroTranslator(excel_file)
    translator.run()

if __name__ == "__main__":
    main()
