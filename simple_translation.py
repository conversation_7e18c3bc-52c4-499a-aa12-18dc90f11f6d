#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版外教自我介绍翻译脚本
"""

import pandas as pd
import re
import time

def clean_html_tags(text):
    """清理HTML标签和特殊字符"""
    if pd.isna(text) or text == '':
        return ''
    
    text = str(text)
    # 替换HTML实体
    text = text.replace('&nbsp;', ' ')
    text = text.replace('&amp;', '&')
    text = text.replace('&lt;', '<')
    text = text.replace('&gt;', '>')
    text = text.replace('&quot;', '"')
    text = text.replace('&#39;', "'")
    
    # 移除HTML标签
    text = re.sub(r'<[^>]+>', '', text)
    
    # 清理多余的空白字符和换行符
    text = re.sub(r'\s+', ' ', text)
    text = text.strip()
    
    return text

def extract_teacher_name(text):
    """提取老师姓名"""
    try:
        patterns = [
            r"(?:I'm|I am|This is|My name is|Hello.*I'm|Hi.*I'm)\s+(?:Teacher\s+)?(\w+)",
            r"Teacher\s+(\w+)",
            r"I'm\s+(\w+)",
            r"call me\s+(\w+)"
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                name = match.group(1)
                # 过滤掉一些常见的非姓名词汇
                if name.lower() not in ['teacher', 'from', 'here', 'your', 'the', 'and', 'with', 'a', 'an']:
                    return name
        return '老师'
    except:
        return '老师'

def extract_experience(text):
    """提取教学经验"""
    try:
        patterns = [
            r'(\d+)\s*years?\s*(?:of\s*)?(?:teaching|experience|teacher)',
            r'teaching\s*(?:for\s*)?(\d+)\s*years?',
            r'(\d+)\s*years?\s*(?:in\s*)?(?:education|teaching)',
            r'more than\s*(\d+)\s*years?',
            r'over\s*(\d+)\s*years?'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                years = int(match.group(1))
                if years > 20:
                    return "20多年"
                elif years > 10:
                    return f"{years}年"
                elif years >= 5:
                    return f"{years}年"
                else:
                    return f"{years}年"
        
        # 检查是否提到经验丰富
        if re.search(r'experienced|professional|qualified|certified', text, re.IGNORECASE):
            return "丰富"
        
        return "丰富"
    except:
        return "丰富"

def extract_specialties(text):
    """提取教学专长"""
    try:
        specialties_map = {
            'conversation': '英语口语',
            'speaking': '英语口语',
            'pronunciation': '英语发音',
            'grammar': '英语语法',
            'business english': '商务英语',
            'kids english': '少儿英语',
            'children': '少儿英语',
            'adult': '成人英语',
            'beginner': '基础英语',
            'advanced': '高级英语',
            'ielts': '雅思',
            'toefl': '托福',
            'writing': '英语写作',
            'reading': '英语阅读'
        }
        
        found_specialties = []
        text_lower = text.lower()
        
        for eng_specialty, cn_specialty in specialties_map.items():
            if eng_specialty in text_lower:
                if cn_specialty not in found_specialties:
                    found_specialties.append(cn_specialty)
        
        if found_specialties:
            return "、".join(found_specialties[:2])  # 最多显示2个专长
        return "英语"
    except:
        return "英语"

def extract_nationality(text):
    """提取国籍信息"""
    try:
        nationality_map = {
            'philippines': '菲律宾',
            'filipino': '菲律宾',
            'american': '美国',
            'usa': '美国',
            'united states': '美国',
            'british': '英国',
            'uk': '英国',
            'england': '英国',
            'canadian': '加拿大',
            'canada': '加拿大',
            'australian': '澳大利亚',
            'australia': '澳大利亚',
            'south africa': '南非',
            'south african': '南非',
            'india': '印度',
            'indian': '印度',
            'pakistan': '巴基斯坦',
            'pakistani': '巴基斯坦',
            'new zealand': '新西兰'
        }
        
        text_lower = text.lower()
        for eng_country, cn_country in nationality_map.items():
            if eng_country in text_lower:
                return cn_country
        return None
    except:
        return None

def create_chinese_intro(english_text):
    """创建适合app展示的中文介绍"""
    try:
        if not english_text or english_text.strip() == '':
            return ''
        
        # 过滤掉无意义的内容
        if english_text.lower().strip() in ['none', 'asd', 'test', 'null', 'n/a']:
            return ''
        
        clean_text = clean_html_tags(english_text)
        
        if len(clean_text) < 10:
            return ''
        
        # 提取关键信息
        teacher_name = extract_teacher_name(clean_text)
        experience = extract_experience(clean_text)
        specialties = extract_specialties(clean_text)
        nationality = extract_nationality(clean_text)
        
        # 构建中文介绍
        intro_parts = []
        
        # 开头问候
        intro_parts.append(f"大家好！我是{teacher_name}")
        
        # 添加国籍信息（如果有）
        if nationality:
            intro_parts.append(f"来自{nationality}")
        
        # 添加教学经验
        intro_parts.append(f"拥有{experience}的教学经验")
        
        # 添加专业信息
        intro_parts.append(f"专业教授{specialties}")
        
        # 添加教学特色
        teaching_style = [
            "我的课堂生动有趣，注重互动交流",
            "致力于帮助每位学生提高英语水平",
            "期待与您一起开启精彩的英语学习之旅！"
        ]
        
        # 组合成完整介绍
        if nationality:
            intro = f"{intro_parts[0]}，{intro_parts[1]}，{intro_parts[2]}，{intro_parts[3]}。{teaching_style[0]}，{teaching_style[1]}，{teaching_style[2]}"
        else:
            intro = f"{intro_parts[0]}，{intro_parts[2]}，{intro_parts[3]}。{teaching_style[0]}，{teaching_style[1]}，{teaching_style[2]}"
        
        return intro
    except Exception as e:
        print(f"翻译出错: {e}")
        return ''

def main():
    print("=== 外教自我介绍翻译工具 ===")
    
    # 读取Excel文件
    print("正在读取Excel文件...")
    try:
        df = pd.read_excel('20250728老师表有英文介绍没有中文介绍全量数据.xlsx')
        print(f"读取成功，共{len(df)}行数据")
    except Exception as e:
        print(f"读取Excel文件失败: {e}")
        return
    
    # 确保有desc_cn列
    if 'desc_cn' not in df.columns:
        df['desc_cn'] = ''
        print("已创建desc_cn列")
    
    # 统计需要处理的数据
    need_processing_mask = (
        df['desc'].notna() & 
        (df['desc'] != '') & 
        (df['desc_cn'].isna() | (df['desc_cn'] == ''))
    )
    
    need_processing_indices = df[need_processing_mask].index.tolist()
    total_count = len(need_processing_indices)
    print(f"需要处理的条目数: {total_count}")
    
    if total_count == 0:
        print("没有需要处理的数据")
        return
    
    print("开始翻译处理...")
    start_time = time.time()
    
    processed_count = 0
    
    # 逐行处理
    for idx in need_processing_indices:
        try:
            english_desc = df.at[idx, 'desc']
            chinese_intro = create_chinese_intro(english_desc)
            df.at[idx, 'desc_cn'] = chinese_intro
            
            processed_count += 1
            
            if processed_count % 1000 == 0:
                progress = (processed_count / total_count) * 100
                elapsed_time = time.time() - start_time
                estimated_total_time = elapsed_time * total_count / processed_count
                remaining_time = estimated_total_time - elapsed_time
                print(f"翻译进度: {processed_count}/{total_count} ({progress:.1f}%) - 预计剩余时间: {remaining_time/60:.1f}分钟")
                
        except Exception as e:
            print(f"处理第{idx}行时出错: {e}")
            df.at[idx, 'desc_cn'] = ''
    
    # 保存结果
    output_file = '20250728老师表有英文介绍没有中文介绍全量数据_translated.xlsx'
    print(f"正在保存到: {output_file}")
    
    try:
        df.to_excel(output_file, index=False)
        print("保存成功！")
        
        # 统计翻译结果
        translated_count = (df['desc_cn'] != '').sum()
        print(f"翻译完成统计:")
        print(f"- 总数据行数: {len(df)}")
        print(f"- 有英文描述的行数: {(df['desc'].notna() & (df['desc'] != '')).sum()}")
        print(f"- 已翻译的行数: {translated_count}")
        
        # 显示几个翻译示例
        print("\n翻译示例:")
        sample_count = 0
        for i in range(len(df)):
            if (pd.notna(df.at[i, 'desc']) and df.at[i, 'desc'] != '' and 
                pd.notna(df.at[i, 'desc_cn']) and df.at[i, 'desc_cn'] != ''):
                print(f"\n示例 {sample_count + 1}:")
                print(f"原文: {str(df.at[i, 'desc'])[:100]}...")
                print(f"译文: {df.at[i, 'desc_cn']}")
                sample_count += 1
                if sample_count >= 3:
                    break
        
        end_time = time.time()
        print(f"\n处理完成！总耗时: {(end_time - start_time)/60:.2f}分钟")
        
    except Exception as e:
        print(f"保存失败: {e}")

if __name__ == "__main__":
    main()
